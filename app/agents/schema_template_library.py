"""
预构建的结构化输出模式模板库。

此模块提供了常用的结构化输出模式模板，可以直接用于配置Agent。
"""

from typing import Dict, List, Optional, Any
from datetime import datetime

from app.agents.structured_output_models import (
    StructuredOutputConfig,
    SchemaDefinition,
    FieldDefinition,
    FieldType,
    ParsingMode,
    ValidationLevel
)


class SchemaTemplateLibrary:
    """模式模板库"""
    
    def __init__(self):
        """初始化模板库"""
        self._templates = self._initialize_templates()
    
    def _initialize_templates(self) -> Dict[str, StructuredOutputConfig]:
        """初始化所有模板"""
        templates = {}
        
        # 情感分析模板
        templates["sentiment_analysis"] = StructuredOutputConfig(
            enabled=True,
            schema_definition=SchemaDefinition(
                class_name="SentimentAnalysis",
                description="文本情感分析结果",
                fields=[
                    FieldDefinition(
                        name="sentiment",
                        type=FieldType.STR,
                        description="情感倾向",
                        required=True,
                        examples=["positive", "negative", "neutral"]
                    ),
                    FieldDefinition(
                        name="confidence",
                        type=FieldType.FLOAT,
                        description="置信度分数",
                        required=True,
                        constraints={"min": 0.0, "max": 1.0},
                        examples=[0.95, 0.8, 0.6]
                    ),
                    FieldDefinition(
                        name="key_phrases",
                        type=FieldType.LIST,
                        description="关键短语列表",
                        required=False,
                        examples=[["产品质量", "服务态度"], ["价格合理"]]
                    )
                ]
            ),
            parsing_mode=ParsingMode.DIRECT,
            validation_level=ValidationLevel.STRICT
        )
        
        # 实体识别模板
        templates["entity_extraction"] = StructuredOutputConfig(
            enabled=True,
            schema_definition=SchemaDefinition(
                class_name="EntityExtraction",
                description="文本实体识别结果",
                fields=[
                    FieldDefinition(
                        name="entities",
                        type=FieldType.LIST,
                        description="提取的实体列表",
                        required=True
                    ),
                    FieldDefinition(
                        name="text",
                        type=FieldType.STR,
                        description="原始文本",
                        required=True
                    ),
                    FieldDefinition(
                        name="language",
                        type=FieldType.STR,
                        description="检测到的语言",
                        required=False,
                        examples=["zh", "en", "ja"]
                    )
                ]
            ),
            parsing_mode=ParsingMode.DIRECT,
            validation_level=ValidationLevel.STRICT
        )
        
        # 问答响应模板
        templates["qa_response"] = StructuredOutputConfig(
            enabled=True,
            schema_definition=SchemaDefinition(
                class_name="QAResponse",
                description="问答系统响应",
                fields=[
                    FieldDefinition(
                        name="answer",
                        type=FieldType.STR,
                        description="答案内容",
                        required=True
                    ),
                    FieldDefinition(
                        name="confidence",
                        type=FieldType.FLOAT,
                        description="答案置信度",
                        required=True,
                        constraints={"min": 0.0, "max": 1.0}
                    ),
                    FieldDefinition(
                        name="sources",
                        type=FieldType.LIST,
                        description="来源信息",
                        required=False
                    ),
                    FieldDefinition(
                        name="follow_up_questions",
                        type=FieldType.LIST,
                        description="后续问题建议",
                        required=False
                    )
                ]
            ),
            parsing_mode=ParsingMode.DIRECT,
            validation_level=ValidationLevel.LENIENT
        )
        
        # 数据分析模板
        templates["data_analysis"] = StructuredOutputConfig(
            enabled=True,
            schema_definition=SchemaDefinition(
                class_name="DataAnalysis",
                description="数据分析结果",
                fields=[
                    FieldDefinition(
                        name="summary",
                        type=FieldType.STR,
                        description="分析摘要",
                        required=True
                    ),
                    FieldDefinition(
                        name="key_insights",
                        type=FieldType.LIST,
                        description="关键洞察",
                        required=True
                    ),
                    FieldDefinition(
                        name="recommendations",
                        type=FieldType.LIST,
                        description="建议列表",
                        required=False
                    ),
                    FieldDefinition(
                        name="data_quality",
                        type=FieldType.DICT,
                        description="数据质量评估",
                        required=False
                    )
                ]
            ),
            parsing_mode=ParsingMode.STRUCTURED_LLM,
            validation_level=ValidationLevel.LENIENT
        )
        
        # 任务规划模板
        templates["task_planning"] = StructuredOutputConfig(
            enabled=True,
            schema_definition=SchemaDefinition(
                class_name="TaskPlan",
                description="任务分解和规划",
                fields=[
                    FieldDefinition(
                        name="main_goal",
                        type=FieldType.STR,
                        description="主要目标",
                        required=True
                    ),
                    FieldDefinition(
                        name="subtasks",
                        type=FieldType.LIST,
                        description="子任务列表",
                        required=True
                    ),
                    FieldDefinition(
                        name="estimated_duration",
                        type=FieldType.STR,
                        description="预估时间",
                        required=False
                    ),
                    FieldDefinition(
                        name="required_resources",
                        type=FieldType.LIST,
                        description="所需资源",
                        required=False
                    ),
                    FieldDefinition(
                        name="potential_risks",
                        type=FieldType.LIST,
                        description="潜在风险",
                        required=False
                    )
                ]
            ),
            parsing_mode=ParsingMode.DIRECT,
            validation_level=ValidationLevel.STRICT
        )
        
        # 代码审查模板
        templates["code_review"] = StructuredOutputConfig(
            enabled=True,
            schema_definition=SchemaDefinition(
                class_name="CodeReview",
                description="代码审查结果",
                fields=[
                    FieldDefinition(
                        name="overall_score",
                        type=FieldType.INT,
                        description="总体评分",
                        required=True,
                        constraints={"min": 1, "max": 10}
                    ),
                    FieldDefinition(
                        name="strengths",
                        type=FieldType.LIST,
                        description="优点列表",
                        required=True
                    ),
                    FieldDefinition(
                        name="issues",
                        type=FieldType.LIST,
                        description="问题列表",
                        required=True
                    ),
                    FieldDefinition(
                        name="suggestions",
                        type=FieldType.LIST,
                        description="改进建议",
                        required=False
                    ),
                    FieldDefinition(
                        name="security_concerns",
                        type=FieldType.LIST,
                        description="安全问题",
                        required=False
                    )
                ]
            ),
            parsing_mode=ParsingMode.DIRECT,
            validation_level=ValidationLevel.STRICT
        )
        
        # 文档生成模板
        templates["documentation"] = StructuredOutputConfig(
            enabled=True,
            schema_definition=SchemaDefinition(
                class_name="Documentation",
                description="文档生成结果",
                fields=[
                    FieldDefinition(
                        name="title",
                        type=FieldType.STR,
                        description="文档标题",
                        required=True
                    ),
                    FieldDefinition(
                        name="sections",
                        type=FieldType.LIST,
                        description="文档章节",
                        required=True
                    ),
                    FieldDefinition(
                        name="summary",
                        type=FieldType.STR,
                        description="文档摘要",
                        required=True
                    ),
                    FieldDefinition(
                        name="keywords",
                        type=FieldType.LIST,
                        description="关键词",
                        required=False
                    ),
                    FieldDefinition(
                        name="metadata",
                        type=FieldType.DICT,
                        description="元数据",
                        required=False
                    )
                ]
            ),
            parsing_mode=ParsingMode.STRUCTURED_LLM,
            validation_level=ValidationLevel.LENIENT
        )
        
        # 决策支持模板
        templates["decision_support"] = StructuredOutputConfig(
            enabled=True,
            schema_definition=SchemaDefinition(
                class_name="DecisionAnalysis",
                description="决策分析结果",
                fields=[
                    FieldDefinition(
                        name="decision",
                        type=FieldType.STR,
                        description="决策建议",
                        required=True
                    ),
                    FieldDefinition(
                        name="alternatives",
                        type=FieldType.LIST,
                        description="备选方案",
                        required=True
                    ),
                    FieldDefinition(
                        name="pros_cons",
                        type=FieldType.DICT,
                        description="优缺点分析",
                        required=True
                    ),
                    FieldDefinition(
                        name="confidence_level",
                        type=FieldType.FLOAT,
                        description="置信度",
                        required=True,
                        constraints={"min": 0.0, "max": 1.0}
                    ),
                    FieldDefinition(
                        name="next_steps",
                        type=FieldType.LIST,
                        description="下一步行动",
                        required=False
                    )
                ]
            ),
            parsing_mode=ParsingMode.DIRECT,
            validation_level=ValidationLevel.STRICT
        )
        
        # 性能分析模板
        templates["performance_analysis"] = StructuredOutputConfig(
            enabled=True,
            schema_definition=SchemaDefinition(
                class_name="PerformanceAnalysis",
                description="性能分析结果",
                fields=[
                    FieldDefinition(
                        name="overall_performance",
                        type=FieldType.STR,
                        description="总体性能评估",
                        required=True
                    ),
                    FieldDefinition(
                        name="metrics",
                        type=FieldType.DICT,
                        description="性能指标",
                        required=True
                    ),
                    FieldDefinition(
                        name="bottlenecks",
                        type=FieldType.LIST,
                        description="性能瓶颈",
                        required=True
                    ),
                    FieldDefinition(
                        name="optimization_suggestions",
                        type=FieldType.LIST,
                        description="优化建议",
                        required=True
                    ),
                    FieldDefinition(
                        name="impact_assessment",
                        type=FieldType.DICT,
                        description="影响评估",
                        required=False
                    )
                ]
            ),
            parsing_mode=ParsingMode.DIRECT,
            validation_level=ValidationLevel.STRICT
        )
        
        return templates
    
    def get_template(self, template_id: str) -> Optional[StructuredOutputConfig]:
        """
        获取指定模板
        
        Args:
            template_id: 模板ID
            
        Returns:
            模板配置，如果不存在则返回None
        """
        return self._templates.get(template_id)
    
    def list_templates(self) -> List[str]:
        """
        列出所有可用模板
        
        Returns:
            模板ID列表
        """
        return list(self._templates.keys())
    
    def get_templates_by_category(self, category: str) -> Dict[str, StructuredOutputConfig]:
        """
        按类别获取模板
        
        Args:
            category: 类别名称
            
        Returns:
            该类别下的模板字典
        """
        category_templates = {
            "text_analysis": ["sentiment_analysis", "entity_extraction"],
            "qa_system": ["qa_response"],
            "data_processing": ["data_analysis", "performance_analysis"],
            "planning": ["task_planning"],
            "development": ["code_review", "documentation"],
            "decision": ["decision_support"]
        }
        
        template_ids = category_templates.get(category, [])
        return {tid: self._templates[tid] for tid in template_ids if tid in self._templates}
    
    def search_templates(self, keyword: str) -> List[str]:
        """
        搜索模板
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的模板ID列表
        """
        keyword = keyword.lower()
        matched_templates = []
        
        for template_id, template in self._templates.items():
            # 搜索模板ID、类名和描述
            if (keyword in template_id.lower() or
                (template.schema_definition and 
                 keyword in template.schema_definition.class_name.lower()) or
                (template.schema_definition and 
                 template.schema_definition.description and
                 keyword in template.schema_definition.description.lower())):
                matched_templates.append(template_id)
        
        return matched_templates
    
    def create_custom_template(self, 
                            template_id: str,
                            class_name: str,
                            description: str,
                            fields: List[FieldDefinition],
                            parsing_mode: ParsingMode = ParsingMode.DIRECT,
                            validation_level: ValidationLevel = ValidationLevel.STRICT) -> StructuredOutputConfig:
        """
        创建自定义模板
        
        Args:
            template_id: 模板ID
            class_name: 类名
            description: 描述
            fields: 字段定义列表
            parsing_mode: 解析模式
            validation_level: 验证级别
            
        Returns:
            创建的模板配置
        """
        template = StructuredOutputConfig(
            enabled=True,
            schema_definition=SchemaDefinition(
                class_name=class_name,
                description=description,
                fields=fields
            ),
            parsing_mode=parsing_mode,
            validation_level=validation_level
        )
        
        # 保存到模板库
        self._templates[template_id] = template
        return template
    
    def get_template_info(self, template_id: str) -> Optional[Dict[str, Any]]:
        """
        获取模板信息
        
        Args:
            template_id: 模板ID
            
        Returns:
            模板信息字典
        """
        template = self._templates.get(template_id)
        if not template:
            return None
        
        return {
            "template_id": template_id,
            "class_name": template.schema_definition.class_name if template.schema_definition else None,
            "description": template.schema_definition.description if template.schema_definition else None,
            "field_count": len(template.schema_definition.fields) if template.schema_definition else 0,
            "parsing_mode": template.parsing_mode.value,
            "validation_level": template.validation_level.value,
            "enabled": template.enabled
        }


# 全局实例
template_library = SchemaTemplateLibrary()


def get_template(template_id: str) -> Optional[StructuredOutputConfig]:
    """获取模板的便捷函数"""
    return template_library.get_template(template_id)


def list_all_templates() -> List[str]:
    """列出所有模板的便捷函数"""
    return template_library.list_templates()


def get_templates_by_category(category: str) -> Dict[str, StructuredOutputConfig]:
    """按类别获取模板的便捷函数"""
    return template_library.get_templates_by_category(category)