"""
动态 Pydantic 模型生成器。

此模块提供了根据结构化输出配置动态生成 Pydantic 模型的功能，
支持多种字段类型、验证规则和自定义代码生成。
"""

import inspect
import importlib
import sys
from typing import Dict, Any, List, Optional, Type, Union
from pydantic import BaseModel, Field, create_model
from pydantic.fields import FieldInfo

from app.agents.structured_output_models import (
    SchemaDefinition, 
    FieldDefinition, 
    FieldType,
    StructuredOutputConfig
)


class DynamicModelGenerator:
    """动态 Pydantic 模型生成器"""
    
    def __init__(self):
        """初始化模型生成器"""
        self._type_mapping = {
            FieldType.STR: str,
            FieldType.INT: int,
            FieldType.FLOAT: float,
            FieldType.BOOL: bool,
            FieldType.DATETIME: str,  # 简化处理，实际使用中可以用 datetime
            FieldType.JSON: Dict[str, Any],
        }
        
        # 复杂类型的处理函数
        self._complex_type_handlers = {
            FieldType.LIST: self._handle_list_type,
            FieldType.DICT: self._handle_dict_type,
        }
    
    def generate_model(self, config: StructuredOutputConfig) -> Optional[Type[BaseModel]]:
        """
        根据结构化输出配置生成 Pydantic 模型
        
        Args:
            config: 结构化输出配置
            
        Returns:
            生成的 Pydantic 模型类，如果配置无效则返回 None
        """
        if not config.enabled or not config.schema_definition:
            return None
            
        schema_def = config.schema_definition
        
        try:
            # 验证类名
            if not schema_def.class_name.isidentifier():
                raise ValueError(f"无效的类名: {schema_def.class_name}")
            
            # 准备字段定义
            fields = {}
            field_validators = {}
            
            for field_def in schema_def.fields:
                field_type, field_info = self._create_field_info(field_def)
                fields[field_def.name] = (field_type, field_info)
                
                # 如果有验证器，添加到验证器字典
                if field_def.constraints:
                    validator_name = f"validate_{field_def.name}"
                    field_validators[validator_name] = self._create_validator(field_def)
            
            # 创建模型
            model = create_model(
                schema_def.class_name,
                __base__=BaseModel,
                __config__={
                    "arbitrary_types_allowed": True
                },
                **fields
            )
            
            # 添加自定义验证器
            if field_validators:
                for validator_name, validator_func in field_validators.items():
                    setattr(model, validator_name, validator_func)
            
            # 添加额外代码
            if schema_def.extra_code:
                self._add_extra_code(model, schema_def.extra_code)
            
            # 添加类文档字符串
            if schema_def.description:
                model.__doc__ = schema_def.description
            
            return model
            
        except Exception as e:
            raise ValueError(f"生成模型失败: {str(e)}")
    
    def _create_field_info(self, field_def: FieldDefinition) -> tuple:
        """创建字段信息，返回 (field_type, field_info) 元组"""
        # 获取字段类型
        field_type = self._get_field_type(field_def)
        
        # 准备字段参数
        field_kwargs = {
            "description": field_def.description
        }
        
        # 添加默认值
        if not field_def.required and field_def.default is not None:
            field_kwargs["default"] = field_def.default
        elif field_def.required:
            field_kwargs["default"] = ...
        else:
            field_kwargs["default"] = None
        
        # 添加示例
        if field_def.examples:
            field_kwargs["examples"] = field_def.examples
        
        # 添加约束
        if field_def.constraints:
            self._apply_constraints(field_kwargs, field_def.constraints, field_def.type)
        
        # 在 Pydantic V2 中，确保字段类型正确
        if field_type is None:
            field_type = str  # 默认为字符串类型
        
        return (field_type, Field(**field_kwargs))
    
    def _get_field_type(self, field_def: FieldDefinition) -> Type:
        """获取字段类型"""
        if field_def.type in self._type_mapping:
            return self._type_mapping[field_def.type]
        elif field_def.type in self._complex_type_handlers:
            return self._complex_type_handlers[field_def.type](field_def)
        else:
            raise ValueError(f"不支持的字段类型: {field_def.type}")
    
    def _handle_list_type(self, field_def: FieldDefinition) -> Type:
        """处理列表类型"""
        # 简化处理，默认为 List[str]
        # 实际可以根据约束或其他信息推断列表元素类型
        return List[str]
    
    def _handle_dict_type(self, field_def: FieldDefinition) -> Type:
        """处理字典类型"""
        return Dict[str, Any]
    
    def _apply_constraints(self, field_kwargs: Dict[str, Any], constraints: Dict[str, Any], field_type: FieldType):
        """应用字段约束"""
        if field_type == FieldType.STR:
            if "min_length" in constraints:
                field_kwargs["min_length"] = constraints["min_length"]
            if "max_length" in constraints:
                field_kwargs["max_length"] = constraints["max_length"]
            if "pattern" in constraints:
                field_kwargs["regex"] = constraints["pattern"]
                
        elif field_type in [FieldType.INT, FieldType.FLOAT]:
            if "min" in constraints:
                field_kwargs["ge"] = constraints["min"]
            if "max" in constraints:
                field_kwargs["le"] = constraints["max"]
                
        elif field_type == FieldType.LIST:
            if "min_items" in constraints:
                field_kwargs["min_items"] = constraints["min_items"]
            if "max_items" in constraints:
                field_kwargs["max_items"] = constraints["max_items"]
    
    def _create_validator(self, field_def: FieldDefinition):
        """创建字段验证器"""
        # 这里可以根据需要创建自定义验证器
        # 简化实现，返回一个空验证器
        def validator(v):
            return v
        return validator
    
    def _add_extra_code(self, model: Type[BaseModel], extra_code: str):
        """添加额外代码到模型"""
        try:
            # 在受限环境中执行额外代码
            # 这里简化处理，实际使用时需要注意安全性
            exec_namespace = {"model": model, "Field": Field}
            exec(extra_code, exec_namespace)
        except Exception as e:
            # 额外代码执行失败不应该影响模型创建
            pass
    
    def validate_schema(self, schema_def: SchemaDefinition) -> List[str]:
        """
        验证模式定义
        
        Args:
            schema_def: 模式定义
            
        Returns:
            验证错误列表，如果为空则表示验证通过
        """
        errors = []
        
        # 验证类名
        if not schema_def.class_name.isidentifier():
            errors.append(f"类名 '{schema_def.class_name}' 不是有效的Python标识符")
        
        # 验证字段名
        field_names = []
        for field in schema_def.fields:
            if not field.name.isidentifier():
                errors.append(f"字段名 '{field.name}' 不是有效的Python标识符")
            
            if field.name in field_names:
                errors.append(f"字段名 '{field.name}' 重复")
            field_names.append(field.name)
            
            # 验证字段类型
            if field.type not in list(FieldType) + list(self._complex_type_handlers.keys()):
                errors.append(f"字段 '{field.name}' 使用了不支持的字段类型: {field.type}")
        
        return errors
    
    def generate_model_code(self, config: StructuredOutputConfig) -> str:
        """
        生成模型的 Python 代码
        
        Args:
            config: 结构化输出配置
            
        Returns:
            生成的 Python 代码
        """
        if not config.enabled or not config.schema_definition:
            return ""
        
        schema_def = config.schema_definition
        
        # 生成导入语句
        imports = ["from pydantic import BaseModel, Field"]
        if schema_def.imports:
            imports.extend(schema_def.imports)
        
        import_code = "\\n".join(imports) + "\\n\\n"
        
        # 生成类定义
        class_doc = f'    """{schema_def.description}"""' if schema_def.description else ""
        class_code = f"class {schema_def.class_name}(BaseModel):\\n{class_doc}\\n"
        
        # 生成字段定义
        field_codes = []
        for field in schema_def.fields:
            field_type = self._get_python_type_string(field.type)
            default_value = self._get_default_value_string(field)
            description = f'    description="{field.description}"'
            
            field_code = f"    {field.name}: {field_type} = Field({default_value}, {description})"
            field_codes.append(field_code)
        
        fields_code = "\\n\\n".join(field_codes)
        
        # 生成额外代码
        extra_code = f"\\n\\n{schema_def.extra_code}" if schema_def.extra_code else ""
        
        return import_code + class_code + fields_code + extra_code
    
    def _get_python_type_string(self, field_type: FieldType) -> str:
        """获取 Python 类型字符串"""
        type_mapping = {
            FieldType.STR: "str",
            FieldType.INT: "int",
            FieldType.FLOAT: "float",
            FieldType.BOOL: "bool",
            FieldType.DATETIME: "str",
            FieldType.JSON: "Dict[str, Any]",
            FieldType.LIST: "List[str]",
            FieldType.DICT: "Dict[str, Any]",
        }
        return type_mapping.get(field_type, "Any")
    
    def _get_default_value_string(self, field_def: FieldDefinition) -> str:
        """获取默认值字符串"""
        if field_def.required:
            return "..."
        elif field_def.default is not None:
            return repr(field_def.default)
        else:
            return "None"


# 全局实例
model_generator = DynamicModelGenerator()


def generate_dynamic_model(config: StructuredOutputConfig) -> Optional[Type[BaseModel]]:
    """
    生成动态模型的便捷函数
    
    Args:
        config: 结构化输出配置
        
    Returns:
        生成的 Pydantic 模型类
    """
    return model_generator.generate_model(config)


def validate_schema_definition(schema_def: SchemaDefinition) -> List[str]:
    """
    验证模式定义的便捷函数
    
    Args:
        schema_def: 模式定义
        
    Returns:
        验证错误列表
    """
    return model_generator.validate_schema(schema_def)